/*
 * Copyright 2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.alibaba.cloud.ai.example.manus.prompt;

import com.alibaba.cloud.ai.example.manus.dynamic.prompt.model.enums.PromptEnum;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test for PromptDescriptionLoader
 */
public class PromptDescriptionLoaderTest {

	private PromptDescriptionLoader loader;

	@BeforeEach
	void setUp() {
		loader = new PromptDescriptionLoader();
		PromptEnum.setDescriptionLoader(loader);
	}

	@Test
	void testLoadEnglishDescription() {
		String description = loader.loadDescription("PLANNING_PLAN_CREATION", "en");
		assertNotNull(description);
		assertFalse(description.isEmpty());
		assertTrue(description.contains("execution plans"));
	}

	@Test
	void testLoadChineseDescription() {
		String description = loader.loadDescription("PLANNING_PLAN_CREATION", "zh");
		assertNotNull(description);
		assertFalse(description.isEmpty());
		assertTrue(description.contains("执行计划"));
	}

	@Test
	void testPromptEnumIntegration() {
		String enDescription = PromptEnum.PLANNING_PLAN_CREATION.getPromptDescriptionForLanguage("en");
		String zhDescription = PromptEnum.PLANNING_PLAN_CREATION.getPromptDescriptionForLanguage("zh");

		assertNotNull(enDescription);
		assertNotNull(zhDescription);
		assertFalse(enDescription.isEmpty());
		assertFalse(zhDescription.isEmpty());
		assertNotEquals(enDescription, zhDescription);
	}

	@Test
	void testNonExistentPrompt() {
		String description = loader.loadDescription("NON_EXISTENT_PROMPT", "en");
		assertEquals("", description);
	}

	@Test
	void testNonExistentLanguage() {
		String description = loader.loadDescription("PLANNING_PLAN_CREATION", "fr");
		assertEquals("", description);
	}

}

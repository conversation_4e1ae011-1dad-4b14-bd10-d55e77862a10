# 中文消息文件

# ManusProperties - 浏览器设置
manus.browser.headless.description=是否使用无头浏览器模式
manus.browser.headless.option.true=是
manus.browser.headless.option.false=否
manus.browser.requestTimeout.description=浏览器请求超时时间(秒)

# ManusProperties - 通用设置
manus.general.debugDetail.description=debug模式 ：会要求模型输出更多内容，方便查找问题，但速度更慢
manus.general.debugDetail.option.true=是
manus.general.debugDetail.option.false=否
manus.general.baseDir.description=manus根目录

# ManusProperties - 交互设置
manus.interaction.openBrowser.description=启动时自动打开浏览器
manus.interaction.openBrowser.option.true=是
manus.interaction.openBrowser.option.false=否

# ManusProperties - 智能体设置
manus.agent.maxSteps.description=智能体执行最大步数
manus.agent.forceOverrideFromYaml.description=强制使用YAML配置文件覆盖同名Agent
manus.agent.forceOverrideFromYaml.option.true=是
manus.agent.forceOverrideFromYaml.option.false=否
manus.agent.userInputTimeout.description=用户输入表单等待超时时间(秒)
manus.agent.maxMemory.description=能记住的最大消息数
manus.agent.parallelToolCalls.description=并行工具调用
manus.agent.parallelToolCalls.option.true=是
manus.agent.parallelToolCalls.option.false=否

# ManusProperties - 无限上下文设置
manus.infiniteContext.enabled.description=是否开启无限上下文
manus.infiniteContext.enabled.option.true=是
manus.infiniteContext.enabled.option.false=否
manus.infiniteContext.parallelThreads.description=并行处理线程数
manus.infiniteContext.taskContextSize.description=触发无限上下文的字符数阈值(字符数)

# ManusProperties - 文件系统设置
manus.filesystem.allowExternalAccess.description=是否允许在工作目录外进行文件操作
manus.filesystem.allowExternalAccess.option.true=是
manus.filesystem.allowExternalAccess.option.false=否

# ManusProperties - MCP服务加载器设置
manus.mcpServiceLoader.connectionTimeoutSeconds.description=MCP连接超时时间(秒)
manus.mcpServiceLoader.maxRetryCount.description=MCP连接最大重试次数
manus.mcpServiceLoader.maxConcurrentConnections.description=MCP最大并发连接数

# PromptEnum 描述
prompt.PLANNING_PLAN_CREATION.description=构建执行计划的Prompt，如果分解任务做的不好，调这个
prompt.AGENT_CURRENT_STEP_ENV.description=用来定义当前的环境信息，对应agent从过去调用的所有函数的结果，也就是当前的环境信息，因为要存到agent里面所以单独有一个项
prompt.AGENT_STEP_EXECUTION.description=每个agent执行步骤时候都会给agent的上下文信息，大部分的变量不要调（因为都是预制的），可以调整一些对他的建议，一个重点的agent步骤执行prompt
prompt.PLANNING_PLAN_FINALIZER.description=用来做最终总结的prompt，对应任务结束以后告知用户的那个动作，已合并用户请求信息
prompt.DIRECT_RESPONSE.description=用于直接反馈模式的prompt，当用户请求无需复杂规划时直接返回结果
prompt.AGENT_STUCK_ERROR.description=Agent执行卡住时的错误提示信息
prompt.SUMMARY_PLAN_TEMPLATE.description=内容总结执行计划的JSON模板
prompt.MAPREDUCE_TOOL_DESCRIPTION.description=MapReduce计划工具的描述信息
prompt.MAPREDUCE_TOOL_PARAMETERS.description=MapReduce计划工具的参数定义JSON
prompt.AGENT_DEBUG_DETAIL_OUTPUT.description=Agent调试模式下的详细输出要求
prompt.AGENT_NORMAL_OUTPUT.description=Agent正常模式下的输出要求
prompt.AGENT_PARALLEL_TOOL_CALLS_RESPONSE.description=Agent并行工具调用的响应规则
prompt.FORM_INPUT_TOOL_DESCRIPTION.description=表单输入工具的描述信息
prompt.FORM_INPUT_TOOL_PARAMETERS.description=表单输入工具的参数定义JSON

# Default messages (English) for Manus application

# ManusProperties - Browser Settings
manus.browser.headless.description=Whether to use headless browser mode
manus.browser.headless.option.true=Yes
manus.browser.headless.option.false=No
manus.browser.requestTimeout.description=Browser request timeout (seconds)

# ManusProperties - General Settings
manus.general.debugDetail.description=Debug mode: requires model to output more content for troubleshooting, but slower
manus.general.debugDetail.option.true=Yes
manus.general.debugDetail.option.false=No
manus.general.baseDir.description=Manus root directory

# ManusProperties - Interaction Settings
manus.interaction.openBrowser.description=Automatically open browser on startup
manus.interaction.openBrowser.option.true=Yes
manus.interaction.openBrowser.option.false=No

# ManusProperties - Agent Settings
manus.agent.maxSteps.description=Maximum execution steps for agent
manus.agent.forceOverrideFromYaml.description=Force override same-name Agent with YAML configuration
manus.agent.forceOverrideFromYaml.option.true=Yes
manus.agent.forceOverrideFromYaml.option.false=No
manus.agent.userInputTimeout.description=User input form timeout (seconds)
manus.agent.maxMemory.description=Maximum number of messages to remember
manus.agent.parallelToolCalls.description=Parallel tool calls
manus.agent.parallelToolCalls.option.true=Yes
manus.agent.parallelToolCalls.option.false=No

# ManusProperties - Infinite Context Settings
manus.infiniteContext.enabled.description=Whether to enable infinite context
manus.infiniteContext.enabled.option.true=Yes
manus.infiniteContext.enabled.option.false=No
manus.infiniteContext.parallelThreads.description=Number of parallel processing threads
manus.infiniteContext.taskContextSize.description=Character count threshold for triggering infinite context

# ManusProperties - File System Settings
manus.filesystem.allowExternalAccess.description=Whether to allow file operations outside the working directory
manus.filesystem.allowExternalAccess.option.true=Yes
manus.filesystem.allowExternalAccess.option.false=No

# ManusProperties - MCP Service Loader Settings
manus.mcpServiceLoader.connectionTimeoutSeconds.description=MCP connection timeout (seconds)
manus.mcpServiceLoader.maxRetryCount.description=MCP connection maximum retry count
manus.mcpServiceLoader.maxConcurrentConnections.description=MCP maximum concurrent connections

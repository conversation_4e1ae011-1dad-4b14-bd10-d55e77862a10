PLANNING_PLAN_CREATION=构建执行计划的Prompt，如果分解任务做的不好，调这个
AGENT_CURRENT_STEP_ENV=用来定义当前的环境信息，对应agent从过去调用的所有函数的结果，也就是当前的环境信息，因为要存到agent里面所以单独有一个项
AGENT_STEP_EXECUTION=每个agent执行步骤时候都会给agent的上下文信息，大部分的变量不要调（因为都是预制的），可以调整一些对他的建议，一个重点的agent步骤执行prompt
PLANNING_PLAN_FINALIZER=用来做最终总结的prompt，对应任务结束以后告知用户的那个动作，已合并用户请求信息
DIRECT_RESPONSE=用于直接反馈模式的prompt，当用户请求无需复杂规划时直接返回结果
AGENT_STUCK_ERROR=Agent执行卡住时的错误提示信息
SUMMARY_PLAN_TEMPLATE=内容总结执行计划的JSON模板
MAPREDUCE_TOOL_DESCRIPTION=MapReduce计划工具的描述信息
MAPREDUCE_TOOL_PARAMETERS=MapReduce计划工具的参数定义JSON
AGENT_DEBUG_DETAIL_OUTPUT=Agent调试模式下的详细输出要求
AGENT_NORMAL_OUTPUT=Agent正常模式下的输出要求
AGENT_PARALLEL_TOOL_CALLS_RESPONSE=Agent并行工具调用的响应规则
FORM_INPUT_TOOL_DESCRIPTION=表单输入工具的描述信息
FORM_INPUT_TOOL_PARAMETERS=表单输入工具的参数定义JSON

PLANNING_PLAN_CREATION=Prompt for building execution plans, adjust this if task decomposition is not working well
AGENT_CURRENT_STEP_ENV=Defines current environment information, corresponding to results from all functions called by agent in the past, stored separately in agent
AGENT_STEP_EXECUTION=Context information given to agent during each execution step, most variables are preset and shouldn't be changed, can adjust some suggestions, a key agent step execution prompt
PLANNING_PLAN_FINALIZER=Prompt for final summary, corresponds to the action of informing users after task completion, merged with user request information
DIRECT_RESPONSE=Prompt for direct response mode, directly returns results when user requests don't need complex planning
AGENT_STUCK_ERROR=Error message when agent execution gets stuck
SUMMARY_PLAN_TEMPLATE=JSON template for content summary execution plan
MAPREDUCE_TOOL_DESCRIPTION=Description for MapReduce planning tool
MAPREDUCE_TOOL_PARAMETERS=Parameter definition JSON for MapReduce planning tool
AGENT_DEBUG_DETAIL_OUTPUT=Detailed output requirements for agent debug mode
AGENT_NORMAL_OUTPUT=Output requirements for agent normal mode
AGENT_PARALLEL_TOOL_CALLS_RESPONSE=Response rules for agent parallel tool calls
FORM_INPUT_TOOL_DESCRIPTION=Description for form input tool
FORM_INPUT_TOOL_PARAMETERS=Parameter definition JSON for form input tool
